"use client";

import { useState, useEffect } from "react";
import { WidgetComponentProps } from "../types/grid";
import { Save, Edit3, FileText } from "lucide-react";
import { Button } from "../components/ui/button";

export default function NotesWidget({
	widget,
	onUpdate,
}: WidgetComponentProps) {
	const [notes, setNotes] = useState("");
	const [isEditing, setIsEditing] = useState(false);
	const [lastSaved, setLastSaved] = useState<Date | null>(null);

	// Load notes from widget config
	useEffect(() => {
		if (widget.config?.notes) {
			setNotes(widget.config.notes);
		}
	}, [widget.config]);

	// Auto-save notes
	useEffect(() => {
		const timer = setTimeout(() => {
			if (notes !== (widget.config?.notes || "")) {
				saveNotes();
			}
		}, 1000);

		return () => clearTimeout(timer);
	}, [notes, widget.config?.notes]);

	const saveNotes = () => {
		onUpdate?.({
			...widget,
			config: { ...widget.config, notes },
		});
		setLastSaved(new Date());
	};

	const isCompact = widget.position.w <= 2 || widget.position.h <= 2;
	const isIconView = widget.position.w === 1 && widget.position.h === 1;

	// 1x1 Icon view
	if (isIconView) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<FileText className="h-6 w-6 text-primary mb-1" />
				<div className="text-xs font-medium text-center">
					{notes.length > 0 ? (
						<div className="flex flex-col items-center">
							<span className="text-foreground">{notes.length}</span>
							<span className="text-muted-foreground text-[10px]">chars</span>
						</div>
					) : (
						<span className="text-muted-foreground">Notes</span>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col gap-3 overflow-hidden">
			{/* Header - Hidden in 1x1 view */}
			{!isIconView && (
				<div className="flex-shrink-0 flex items-center justify-between">
					<div>
						<h3 className="text-sm font-semibold text-foreground">
							{isCompact ? "Notes" : "Quick Notes"}
						</h3>
						{!isCompact && lastSaved && (
							<p className="text-xs text-muted-foreground">
								Saved {lastSaved.toLocaleTimeString()}
							</p>
						)}
					</div>
					<div className="flex items-center gap-1">
						<Button
							size="sm"
							variant="ghost"
							onClick={() => setIsEditing(!isEditing)}
							className={`p-0 ${isCompact ? "h-5 w-5" : "h-6 w-6"}`}
						>
							<Edit3 className={`${isCompact ? "h-2 w-2" : "h-3 w-3"}`} />
						</Button>
						{!isCompact && (
							<Button
								size="sm"
								variant="ghost"
								onClick={saveNotes}
								className="h-6 w-6 p-0"
							>
								<Save className="h-3 w-3" />
							</Button>
						)}
					</div>
				</div>
			)}

			{/* Notes Content */}
			<div className="flex-1 overflow-hidden">
				{isEditing || notes === "" ? (
					<textarea
						value={notes}
						onChange={(e) => setNotes(e.target.value)}
						placeholder={
							isCompact ? "Write notes..." : "Write your notes here..."
						}
						className={`w-full h-full bg-background border border-input rounded text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring resize-none ${
							isCompact ? "p-1 text-[10px]" : "p-2 text-xs"
						}`}
						autoFocus={isEditing}
					/>
				) : (
					<div
						className={`w-full h-full bg-muted rounded overflow-y-auto cursor-pointer hover:bg-muted/80 transition-colors ${
							isCompact ? "p-1 text-[10px]" : "p-2 text-xs"
						}`}
						onClick={() => setIsEditing(true)}
					>
						{notes ? (
							<pre className="whitespace-pre-wrap text-foreground font-sans">
								{notes}
							</pre>
						) : (
							<p className="text-muted-foreground italic">
								{isCompact ? "Click to add..." : "Click to add notes..."}
							</p>
						)}
					</div>
				)}
			</div>

			{/* Character Count */}
			{!isCompact && notes.length > 0 && (
				<div className="flex-shrink-0 text-xs text-muted-foreground text-right">
					{notes.length} characters
				</div>
			)}
		</div>
	);
}
