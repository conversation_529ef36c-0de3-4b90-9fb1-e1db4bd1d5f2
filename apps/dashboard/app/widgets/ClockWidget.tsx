"use client";

import { useState, useEffect } from "react";
import { WidgetComponentProps } from "../types/grid";
import { Clock, Globe } from "lucide-react";

export default function ClockWidget({ widget }: WidgetComponentProps) {
	const [time, setTime] = useState<Date | null>(null);
	const [timezone, setTimezone] = useState("local");

	useEffect(() => {
		// Set initial time on client side to avoid hydration mismatch
		setTime(new Date());

		const timer = setInterval(() => {
			setTime(new Date());
		}, 1000);

		return () => clearInterval(timer);
	}, []);

	const formatTime = (date: Date) => {
		if (timezone === "local") {
			return date.toLocaleTimeString([], {
				hour: "2-digit",
				minute: "2-digit",
				second: "2-digit",
			});
		}
		// Add timezone support later
		return date.toLocaleTimeString([], {
			hour: "2-digit",
			minute: "2-digit",
			second: "2-digit",
		});
	};

	const formatDate = (date: Date) => {
		return date.toLocaleDateString([], {
			weekday: "short",
			month: "short",
			day: "numeric",
		});
	};

	const isCompact = widget.position.w <= 2 || widget.position.h <= 1;
	const isIconView = widget.position.w === 1 && widget.position.h === 1;

	// Show loading state until time is set to avoid hydration mismatch
	if (!time) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<Clock className="h-6 w-6 text-muted-foreground" />
				<div className="text-xs text-muted-foreground">Loading...</div>
			</div>
		);
	}

	// 1x1 Icon view
	if (isIconView) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<Clock className="h-6 w-6 text-primary mb-1" />
				<div className="text-xs font-medium text-center">
					<div className="flex flex-col items-center">
						<span className="text-foreground font-mono text-[10px]">
							{formatTime(time).slice(0, 5)}
						</span>
						<span className="text-muted-foreground text-[10px]">time</span>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col items-center justify-center gap-2">
			{/* Time Display */}
			<div className="text-center">
				<div
					className={`font-mono font-bold text-foreground ${
						isCompact ? "text-lg" : "text-2xl"
					}`}
				>
					{formatTime(time)}
				</div>
				{!isCompact && (
					<div className="text-sm text-muted-foreground">
						{formatDate(time)}
					</div>
				)}
			</div>

			{/* Timezone Indicator */}
			{!isCompact && (
				<div className="flex items-center gap-1 text-xs text-muted-foreground">
					<Globe className="h-3 w-3" />
					<span>Local Time</span>
				</div>
			)}
		</div>
	);
}
