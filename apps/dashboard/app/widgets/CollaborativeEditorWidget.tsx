"use client";

import { useEffect, useRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Collaboration from "@tiptap/extension-collaboration";
import { HocuspocusProvider } from "@hocuspocus/provider";
import * as Y from "yjs";
import { WidgetComponentProps } from "../types/grid";
import { Button } from "../components/ui/button";
import { Plus, Trash2, Edit3 } from "lucide-react";

export default function CollaborativeEditorWidget({
	widget,
}: WidgetComponentProps) {
	const ydocRef = useRef(new Y.Doc());
	const [providerReady, setProviderReady] = useState(false);
	const [tasks, setTasks] = useState<string[]>([]);
	const [lastTaskAdded, setLastTaskAdded] = useState("");
	const [taskInput, setTaskInput] = useState("");

	useEffect(() => {
		const provider = new HocuspocusProvider({
			url: "ws://*************:1234",
			name: `CollabDocument-${widget.id}`,
			document: ydocRef.current,
			token: "valid-token",
			onConnect() {
				console.log("✅ Connected");
				setProviderReady(true);
				const yTasks = ydocRef.current.getArray("tasks");
				yTasks.observe(() => {
					setTasks(
						yTasks.toArray().filter((t): t is string => typeof t === "string")
					);
				});
			},
		});

		return () => {
			provider.destroy();
			console.log("🧹 Provider destroyed");
		};
	}, [widget.id]);

	const editor = useEditor(
		{
			extensions: [
				StarterKit.configure({ history: false }),
				Collaboration.configure({ document: ydocRef.current }),
			] as any,
			content: "",
			immediatelyRender: false,
		},
		[providerReady]
	);

	const handleAddTask = () => {
		const task = taskInput.trim();
		if (!task) return;
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.push([task]);
		setLastTaskAdded(task);
		setTaskInput("");
	};

	const handleClearTasks = () => {
		const yTasks = ydocRef.current.getArray("tasks");
		yTasks.delete(0, yTasks.length);
		setTasks([]);
		setLastTaskAdded("");
	};

	// Determine layout based on widget size
	const isCompact = widget.position.w <= 2 || widget.position.h <= 2;
	const isLarge = widget.position.w >= 4 && widget.position.h >= 3;
	const isIconView = widget.position.w === 1 && widget.position.h === 1;

	// 1x1 Icon view
	if (isIconView) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<Edit3 className="h-6 w-6 text-primary mb-1" />
				<div className="text-xs font-medium text-center">
					<div className="flex flex-col items-center">
						<span className="text-foreground">{tasks.length}</span>
						<span className="text-muted-foreground text-[10px]">collab</span>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col gap-3 overflow-hidden">
			{/* Tasks Section */}
			{!isCompact && (
				<div className="flex-shrink-0">
					<h3 className="text-sm font-semibold text-foreground mb-2">Tasks</h3>
					{tasks.length > 0 ? (
						<div className="space-y-1 max-h-20 overflow-y-auto">
							{tasks.slice(0, isLarge ? 5 : 3).map((task, index) => (
								<div
									key={index}
									className="flex items-center gap-2 text-xs p-1 bg-muted rounded"
								>
									<span className="text-primary">•</span>
									<span className="text-foreground truncate">{task}</span>
								</div>
							))}
							{tasks.length > (isLarge ? 5 : 3) && (
								<div className="text-xs text-muted-foreground">
									+{tasks.length - (isLarge ? 5 : 3)} more
								</div>
							)}
						</div>
					) : (
						<p className="text-xs text-muted-foreground italic">No tasks yet</p>
					)}
				</div>
			)}

			{/* Task Input */}
			{!isCompact && (
				<div className="flex-shrink-0 flex gap-2">
					<input
						type="text"
						value={taskInput}
						onChange={(e) => setTaskInput(e.target.value)}
						onKeyDown={(e) => e.key === "Enter" && handleAddTask()}
						placeholder="Add task..."
						className="flex-1 px-2 py-1 text-xs bg-background border border-input rounded text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring"
					/>
					<Button
						size="sm"
						onClick={handleAddTask}
						disabled={!taskInput.trim()}
						className="h-6 w-6 p-0"
					>
						<Plus className="h-3 w-3" />
					</Button>
					{tasks.length > 0 && (
						<Button
							size="sm"
							variant="destructive"
							onClick={handleClearTasks}
							className="h-6 w-6 p-0"
						>
							<Trash2 className="h-3 w-3" />
						</Button>
					)}
				</div>
			)}

			{/* Success Message */}
			{lastTaskAdded && !isCompact && (
				<div className="flex-shrink-0 p-2 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded text-xs">
					<p className="text-green-700 dark:text-green-300">
						✅ Added: <strong>{lastTaskAdded}</strong>
					</p>
				</div>
			)}

			{/* Editor */}
			<div className="flex-1 border border-border rounded overflow-hidden">
				<div className="bg-muted px-2 py-1 border-b border-border">
					<h4 className="text-xs font-medium text-foreground">
						{isCompact ? "Editor" : "Collaborative Editor"}
					</h4>
				</div>
				<div className="p-2 h-full overflow-auto">
					{editor ? (
						<EditorContent
							editor={editor}
							className="prose prose-xs max-w-none focus:outline-none text-foreground"
						/>
					) : (
						<div className="flex items-center justify-center h-full">
							<p className="text-xs text-muted-foreground">Loading...</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
