"use client";

import { useState, useEffect } from "react";
import { WidgetComponentProps } from "../types/grid";
import { Button } from "../components/ui/button";
import { Plus, Trash2, Check, CheckSquare } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface Task {
	id: string;
	text: string;
	completed: boolean;
	createdAt: Date;
}

export default function TaskListWidget({
	widget,
	onUpdate,
}: WidgetComponentProps) {
	const [tasks, setTasks] = useState<Task[]>([]);
	const [newTaskText, setNewTaskText] = useState("");

	// Load tasks from widget config
	useEffect(() => {
		if (widget.config?.tasks) {
			setTasks(widget.config.tasks);
		}
	}, [widget.config]);

	// Save tasks to widget config
	const saveTasks = (updatedTasks: Task[]) => {
		setTasks(updatedTasks);
		onUpdate?.({
			...widget,
			config: { ...widget.config, tasks: updatedTasks },
		});
	};

	const addTask = () => {
		if (!newTaskText.trim()) return;

		const newTask: Task = {
			id: `task-${Date.now()}`,
			text: newTaskText.trim(),
			completed: false,
			createdAt: new Date(),
		};

		saveTasks([...tasks, newTask]);
		setNewTaskText("");
	};

	const toggleTask = (taskId: string) => {
		const updatedTasks = tasks.map((task) =>
			task.id === taskId ? { ...task, completed: !task.completed } : task
		);
		saveTasks(updatedTasks);
	};

	const removeTask = (taskId: string) => {
		const updatedTasks = tasks.filter((task) => task.id !== taskId);
		saveTasks(updatedTasks);
	};

	const clearCompleted = () => {
		const updatedTasks = tasks.filter((task) => !task.completed);
		saveTasks(updatedTasks);
	};

	const completedCount = tasks.filter((task) => task.completed).length;
	const isCompact = widget.position.w <= 2 || widget.position.h <= 2;
	const isIconView = widget.position.w === 1 && widget.position.h === 1;

	// 1x1 Icon view
	if (isIconView) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<CheckSquare className="h-6 w-6 text-primary mb-1" />
				<div className="text-xs font-medium text-center">
					{tasks.length > 0 ? (
						<div className="flex flex-col items-center">
							<span className="text-foreground">{tasks.length}</span>
							<span className="text-muted-foreground text-[10px]">tasks</span>
						</div>
					) : (
						<span className="text-muted-foreground">Tasks</span>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col gap-3 overflow-hidden">
			{/* Header - Hidden in 1x1 view */}
			{!isIconView && (
				<div className="flex-shrink-0 flex items-center justify-between">
					<div>
						<h3 className="text-sm font-semibold text-foreground">
							{isCompact ? "Tasks" : "Task List"}
						</h3>
						{!isCompact && tasks.length > 0 && (
							<p className="text-xs text-muted-foreground">
								{completedCount} of {tasks.length} completed
							</p>
						)}
					</div>
					{completedCount > 0 && (
						<Button
							size="sm"
							variant="ghost"
							onClick={clearCompleted}
							className="h-6 px-2 text-xs"
						>
							Clear
						</Button>
					)}
				</div>
			)}

			{/* Add Task */}
			<div className="flex-shrink-0 flex gap-2">
				<input
					type="text"
					value={newTaskText}
					onChange={(e) => setNewTaskText(e.target.value)}
					onKeyDown={(e) => e.key === "Enter" && addTask()}
					placeholder={isCompact ? "Add..." : "Add new task..."}
					className={`flex-1 px-2 py-1 text-xs bg-background border border-input rounded text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring ${
						isCompact ? "h-5" : "h-6"
					}`}
				/>
				<Button
					size="sm"
					onClick={addTask}
					disabled={!newTaskText.trim()}
					className={`p-0 ${isCompact ? "h-5 w-5" : "h-6 w-6"}`}
				>
					<Plus className={`${isCompact ? "h-2 w-2" : "h-3 w-3"}`} />
				</Button>
			</div>

			{/* Task List */}
			<div className="flex-1 overflow-y-auto space-y-1">
				<AnimatePresence>
					{tasks.map((task) => (
						<motion.div
							key={task.id}
							initial={{ opacity: 0, y: -10 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, x: -100 }}
							className={`flex items-center gap-2 bg-muted rounded group ${
								isCompact ? "p-1" : "p-2"
							}`}
						>
							<button
								onClick={() => toggleTask(task.id)}
								className={`flex-shrink-0 rounded border-2 flex items-center justify-center transition-colors ${
									isCompact ? "w-3 h-3" : "w-4 h-4"
								} ${
									task.completed
										? "bg-primary border-primary text-primary-foreground"
										: "border-input hover:border-primary"
								}`}
							>
								{task.completed && (
									<Check
										className={`${isCompact ? "h-1.5 w-1.5" : "h-2 w-2"}`}
									/>
								)}
							</button>

							<span
								className={`flex-1 truncate transition-all ${
									isCompact ? "text-[10px]" : "text-xs"
								} ${
									task.completed
										? "text-muted-foreground line-through"
										: "text-foreground"
								}`}
							>
								{task.text}
							</span>

							{!isCompact && (
								<Button
									size="sm"
									variant="ghost"
									onClick={() => removeTask(task.id)}
									className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
								>
									<Trash2 className="h-2 w-2" />
								</Button>
							)}
						</motion.div>
					))}
				</AnimatePresence>

				{tasks.length === 0 && (
					<div className="flex items-center justify-center h-full">
						<p className="text-xs text-muted-foreground italic">
							No tasks yet. Add one above!
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
