"use client";

import React, { useState } from "react";
import { Globe, ExternalLink, RefreshCw, Settings } from "lucide-react";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { WidgetComponentProps } from "../types/grid";

const WebViewWidget: React.FC<WidgetComponentProps> = ({
	widget,
	onUpdate,
}) => {
	const [url, setUrl] = useState(widget.config?.url || "https://example.com");
	const [isEditing, setIsEditing] = useState(false);
	const [loading, setLoading] = useState(false);

	const handleUrlChange = (newUrl: string) => {
		setUrl(newUrl);
		if (onUpdate) {
			onUpdate({
				...widget,
				config: { ...widget.config, url: newUrl },
			});
		}
	};

	const handleRefresh = () => {
		setLoading(true);
		// Simulate refresh delay
		setTimeout(() => setLoading(false), 1000);
	};

	const openInNewTab = () => {
		window.open(url, "_blank");
	};

	const isValidUrl = (urlString: string) => {
		try {
			new URL(urlString);
			return true;
		} catch {
			return false;
		}
	};

	const isCompact = widget.position.w === 2 && widget.position.h === 2;
	const isIconView = widget.position.w === 1 && widget.position.h === 1;

	// 1x1 Icon view
	if (isIconView) {
		return (
			<div className="h-full flex flex-col items-center justify-center">
				<Globe className="h-6 w-6 text-primary mb-1" />
				<div className="text-xs font-medium text-center">
					<div className="flex flex-col items-center">
						<span className="text-foreground text-[10px] truncate max-w-full">
							{url.replace(/^https?:\/\//, "").split("/")[0]}
						</span>
						<span className="text-muted-foreground text-[10px]">web</span>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col">
			{/* Header - Hidden in 1x1 view */}
			{!isIconView && (
				<div
					className={`flex items-center justify-between ${isCompact ? "mb-2" : "mb-3"}`}
				>
					<div className="flex items-center gap-2 flex-1 min-w-0">
						<Globe
							className={`text-primary flex-shrink-0 ${isCompact ? "h-3 w-3" : "h-4 w-4"}`}
						/>
						{isEditing ? (
							<Input
								value={url}
								onChange={(e) => setUrl(e.target.value)}
								onBlur={() => {
									setIsEditing(false);
									handleUrlChange(url);
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										setIsEditing(false);
										handleUrlChange(url);
									}
								}}
								className={`text-xs ${isCompact ? "h-5" : "h-6"}`}
								placeholder="Enter URL..."
								autoFocus
							/>
						) : (
							<span
								className={`text-muted-foreground truncate cursor-pointer hover:text-foreground ${
									isCompact ? "text-[10px]" : "text-xs"
								}`}
								onClick={() => setIsEditing(true)}
								title={url}
							>
								{url.replace(/^https?:\/\//, "")}
							</span>
						)}
					</div>

					<div className="flex gap-1 flex-shrink-0">
						{!isCompact && (
							<Button
								variant="ghost"
								size="sm"
								className="h-6 w-6 p-0"
								onClick={() => setIsEditing(!isEditing)}
							>
								<Settings className="h-3 w-3" />
							</Button>
						)}
						<Button
							variant="ghost"
							size="sm"
							className={`p-0 ${isCompact ? "h-5 w-5" : "h-6 w-6"}`}
							onClick={handleRefresh}
							disabled={loading}
						>
							<RefreshCw
								className={`${isCompact ? "h-2 w-2" : "h-3 w-3"} ${loading ? "animate-spin" : ""}`}
							/>
						</Button>
						{!isCompact && (
							<Button
								variant="ghost"
								size="sm"
								className="h-6 w-6 p-0"
								onClick={openInNewTab}
							>
								<ExternalLink className="h-3 w-3" />
							</Button>
						)}
					</div>
				</div>
			)}

			{/* Web content area */}
			<div className="flex-1 border rounded bg-background overflow-hidden">
				{isValidUrl(url) ? (
					<iframe
						src={url}
						className="w-full h-full border-0"
						title="Web View"
						sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
						loading="lazy"
					/>
				) : (
					<div className="h-full flex flex-col items-center justify-center text-muted-foreground">
						<Globe className="h-8 w-8 mb-2" />
						<div className="text-sm">Invalid URL</div>
						<div className="text-xs mt-1">Please enter a valid web address</div>
					</div>
				)}
			</div>

			{/* Footer info for larger widgets */}
			{!isCompact && (
				<div className="mt-2 text-xs text-muted-foreground">
					<div className="flex items-center justify-between">
						<span>Web View</span>
						<span
							className={`w-2 h-2 rounded-full ${loading ? "bg-yellow-500" : "bg-green-500"}`}
						/>
					</div>
				</div>
			)}
		</div>
	);
};

export default WebViewWidget;
