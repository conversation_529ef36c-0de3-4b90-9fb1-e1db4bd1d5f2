"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import {
	Plus,
	Settings,
	Users,
	Sun,
	Moon,
	Paintbrush,
	ChevronRight,
	Lightbulb,
	FileText,
	ListTodo,
	Link,
	HelpCircle,
	MessageSquare,
	Bell,
	Save,
	Type,
	LayoutGrid,
} from "lucide-react";
import AppLogo from "@/app/components/AppLogo";
import { Button } from "@/app/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/app/components/ui/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/app/components/ui/select";
import { Switch } from "@/app/components/ui/switch";
import { Label } from "@/app/components/ui/label";
import { PillColorSettings } from "./PillColorSettings";
import WidgetMarketplace from "@/app/components/WidgetMarketplace";

const groups = [
	{ id: 1, name: "Product Team", seeds: ["product-1", "product-2"] },
	{ id: 2, name: "Design Squad", seeds: ["design-1", "design-2", "design-3"] },
	{ id: 3, name: "Dev Crew", seeds: ["dev-1", "dev-2", "dev-3", "dev-4"] },
	{
		id: 4,
		name: "Marketing Team",
		seeds: [
			"marketing-1",
			"marketing-2",
			"marketing-3",
			"marketing-4",
			"marketing-5",
		],
	},
];

const backgroundColors = ["b6e3f4", "ffd5dc", "ffe9b1", "bce1b6", "c0aede"];

const getAvatarUrl = (seed: string, colorIndex: number) => {
	return `https://api.dicebear.com/6.x/micah/svg?seed=${seed}&backgroundColor=${
		backgroundColors[colorIndex % backgroundColors.length]
	}`;
};

const GroupAvatar = ({ seeds, name }: { seeds: string[]; name: string }) => {
	const [isHovered, setIsHovered] = useState(false);

	const getPositions = (
		index: number,
		total: number
	): { x: number; y: number } => {
		if (total === 1) {
			return { x: 0, y: 0 };
		} else if (total === 2) {
			return { x: +(index * 10 - 5).toFixed(2), y: 0 };
		} else if (total === 3) {
			const positions = [
				{ x: 0, y: -6 },
				{ x: -9, y: 6 },
				{ x: 9, y: 6 },
			];
			// Round all positions to 2 decimals
			const pos = positions[index] || { x: 0, y: 0 };
			return { x: +pos.x.toFixed(2), y: +pos.y.toFixed(2) };
		} else if (total === 4) {
			const positions = [
				{ x: -7, y: -7 },
				{ x: 7, y: -7 },
				{ x: -7, y: 7 },
				{ x: 7, y: 7 },
			];
			const pos = positions[index] || { x: 0, y: 0 };
			return { x: +pos.x.toFixed(2), y: +pos.y.toFixed(2) };
		} else {
			const angle = (index / total) * 2 * Math.PI;
			const radius = 10;
			// Round to 2 decimals for deterministic SSR/client output
			const x = Math.round(Math.cos(angle) * radius * 100) / 100;
			const y = Math.round(Math.sin(angle) * radius * 100) / 100;
			return { x, y };
		}
	};

	const getHoverPositions = (index: number, total: number) => {
		const radius = 18;
		const angle = (index / total) * 2 * Math.PI - Math.PI / 2;
		// Round to 2 decimal places for deterministic SSR/client output
		const x = Math.round(Math.cos(angle) * radius * 100) / 100;
		const y = Math.round(Math.sin(angle) * radius * 100) / 100;
		return { x: +x.toFixed(2), y: +y.toFixed(2) };
	};

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div
						className="relative h-14 w-14 flex items-center justify-center"
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<AnimatePresence>
							{seeds.map((seed, index) => {
								const { x, y } = getPositions(index, seeds.length);
								const hoverPos = getHoverPositions(index, seeds.length);
								return (
									<motion.div
										key={`${seed}-${index}`}
										className="absolute h-8 w-8 rounded-full overflow-hidden border-2 border-white dark:border-gray-800"
										initial={{ x, y, opacity: seeds.length > 4 ? 0 : 1 }}
										animate={{
											x: isHovered ? hoverPos.x : x,
											y: isHovered ? hoverPos.y : y,
											opacity: 1,
											scale: isHovered ? 1.1 : 1,
											zIndex: isHovered ? 2 : 1,
										}}
										exit={{ opacity: 0, scale: 0.5 }}
										transition={{
											type: "spring",
											stiffness: 400,
											damping: 25,
										}}
									>
										<Image
											src={getAvatarUrl(`${seed}-${index}`, index)}
											alt={`${name} member ${index + 1}`}
											width={32}
											height={32}
											className="h-full w-full"
										/>
									</motion.div>
								);
							})}
						</AnimatePresence>
						{seeds.length > 4 && (
							<motion.div
								className="absolute h-14 w-14 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-sm font-medium"
								initial={{ scale: 1, opacity: 1 }}
								animate={{
									scale: isHovered ? 0 : 1,
									opacity: isHovered ? 0 : 1,
								}}
							>
								<Users className="h-6 w-6" />
								<span className="ml-1">{seeds.length}</span>
							</motion.div>
						)}
					</div>
				</TooltipTrigger>
				<TooltipContent
					side="right"
					sideOffset={16}
					align="start"
					alignOffset={-20}
					className="p-0"
				>
					<div className="p-2">
						<span className="font-semibold mb-1 block">{name}</span>
						<div className="flex flex-wrap gap-2">
							{thoughtCategories.map((category, index) => (
								<div
									key={index}
									onClick={() => {
										/* Handle click */
									}}
									className="p-1 hover:bg-gray-100 rounded-full transition-colors flex items-center space-x-1 cursor-pointer"
									title={`${category.name}: ${category.count}`}
								>
									{category.icon}
									<span className="text-xs font-medium">{category.count}</span>
								</div>
							))}
						</div>
					</div>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};

const colorOptions = [
	{ name: "None", class: "bg-none", gradientClass: "" },
	{
		name: "Slate",
		class: "bg-slate-500",
		gradientClass: "bg-gradient-to-r from-slate-400 to-slate-600",
	},
	{
		name: "Red",
		class: "bg-red-500",
		gradientClass: "bg-gradient-to-r from-red-400 to-red-600",
	},
	{
		name: "Green",
		class: "bg-green-500",
		gradientClass: "bg-gradient-to-r from-green-400 to-green-600",
	},
	{
		name: "Blue",
		class: "bg-blue-500",
		gradientClass: "bg-gradient-to-r from-blue-400 to-blue-600",
	},
	{
		name: "Purple",
		class: "bg-purple-500",
		gradientClass: "bg-gradient-to-r from-purple-400 to-purple-600",
	},
];

const pillStyles = [
	{ name: "Basic", value: "basic" },
	{ name: "Gradient", value: "gradient" },
] as const;

type PillStyle = (typeof pillStyles)[number]["value"];

const thoughtCategories = [
	{
		icon: <Lightbulb className="w-4 h-4 text-yellow-500" />,
		name: "Idea",
		count: 5,
	},
	{
		icon: <FileText className="w-4 h-4 text-blue-500" />,
		name: "Note",
		count: 3,
	},
	{
		icon: <ListTodo className="w-4 h-4 text-green-500" />,
		name: "Task",
		count: 8,
	},
	{
		icon: <Link className="w-4 h-4 text-purple-500" />,
		name: "Resource",
		count: 2,
	},
	{
		icon: <HelpCircle className="w-4 h-4 text-red-500" />,
		name: "Question",
		count: 4,
	},
	{
		icon: <MessageSquare className="w-4 h-4 text-orange-500" />,
		name: "Feed Post",
		count: 6,
	},
];

export default function DashboardNav() {
	const [activeGroup, setActiveGroup] = useState<number | null>(null);
	const [isDarkMode, setIsDarkMode] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [currentView, setCurrentView] = useState<
		| "main"
		| "addThought"
		| "settings"
		| "customizePill"
		| "notifications"
		| "autoSave"
		| "fontSize"
		| "widgetMarketplace"
	>("main");
	const [pillStyle, setPillStyle] = useState<PillStyle>("basic");
	const [pillColor, setPillColor] = useState("bg-none");
	const [isOutlined, setIsOutlined] = useState(false);
	const [showNotifications, setShowNotifications] = useState(true);
	const [autoSave, setAutoSave] = useState(true);
	const [fontSize, setFontSize] = useState("medium");

	useEffect(() => {
		const isDark = localStorage.getItem("darkMode") === "true";
		setIsDarkMode(isDark);
		document.documentElement.classList.toggle("dark", isDark);
	}, []);

	const toggleDarkMode = () => {
		const newDarkMode = !isDarkMode;
		setIsDarkMode(newDarkMode);
		localStorage.setItem("darkMode", newDarkMode.toString());
		document.documentElement.classList.toggle("dark", newDarkMode);
	};

	const getPillStyle = (style: PillStyle, color: string, outlined: boolean) => {
		let baseStyle = "";
		let textColor = "";

		if (color === "bg-none") {
			baseStyle = isDarkMode ? "bg-gray-800" : "bg-white";
			textColor = isDarkMode ? "text-white" : "text-gray-800";
		} else {
			const baseColor = color.split("-")[1];
			switch (style) {
				case "basic":
					baseStyle = color;
					textColor = "text-white";
					break;
				case "gradient":
					baseStyle = `bg-gradient-to-r from-${baseColor}-400 to-${baseColor}-600`;
					textColor = "text-white";
					break;
				default:
					baseStyle = color;
					textColor = "text-white";
			}
		}

		const outlineStyle = outlined
			? `ring-2 ${isDarkMode ? "ring-white" : "ring-gray-800"}`
			: "";

		return `relative overflow-hidden ${baseStyle} ${textColor} ${outlineStyle}`;
	};

	const renderDropdownContent = () => {
		return (
			<AnimatePresence mode="wait">
				<motion.div
					key={currentView}
					initial={{ opacity: 0, x: -20 }}
					animate={{ opacity: 1, x: 0 }}
					exit={{ opacity: 0, x: 20 }}
					transition={{ duration: 0.2 }}
				>
					{(() => {
						switch (currentView) {
							case "main":
								return (
									<>
										<h3 className="text-lg font-semibold mb-3">
											Quick Actions
										</h3>
										<div className="space-y-1.5">
											<Button
												variant="outline"
												className="w-full justify-start pr-1"
												onClick={() => setCurrentView("addThought")}
											>
												<Lightbulb className="mr-2 h-4 w-4" />
												<span>Add a Thought</span>
											</Button>
											<Button
												variant="outline"
												className="w-full justify-start pr-1"
												onClick={() => setCurrentView("widgetMarketplace")}
											>
												<LayoutGrid className="mr-2 h-4 w-4" />
												<span>Add a Widget</span>
											</Button>
											<Button
												variant="outline"
												className="w-full justify-start pr-1"
												onClick={() => setCurrentView("settings")}
											>
												<Settings className="mr-2 h-4 w-4" />
												<span>Settings</span>
											</Button>
										</div>
									</>
								);
							case "addThought":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("main")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">
											Add a Thought
										</h3>
										<div className="grid grid-cols-2 gap-2">
											{thoughtCategories.map((category, index) => (
												<Button
													key={category.name}
													variant="outline"
													className="w-full justify-start pr-1"
												>
													{category.icon}
													<span>{category.name}</span>
												</Button>
											))}
										</div>
									</>
								);
							case "settings":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("main")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">Settings</h3>
										<div className="space-y-2">
											<Button
												variant="outline"
												className="w-full justify-start"
												onClick={() => setCurrentView("customizePill")}
											>
												<Paintbrush className="mr-2 h-4 w-4" />
												<span>Customize Pill</span>
											</Button>
											<Button
												variant="outline"
												className="w-full justify-start"
												onClick={() => setCurrentView("notifications")}
											>
												<Bell className="mr-2 h-4 w-4" />
												<span>Notifications</span>
											</Button>
											<Button
												variant="outline"
												className="w-full justify-start"
												onClick={() => setCurrentView("autoSave")}
											>
												<Save className="mr-2 h-4 w-4" />
												<span>Auto Save</span>
											</Button>
											<Button
												variant="outline"
												className="w-full justify-start"
												onClick={() => setCurrentView("fontSize")}
											>
												<Type className="mr-2 h-4 w-4" />
												<span>Font Size</span>
											</Button>
										</div>
									</>
								);
							case "customizePill":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("settings")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back to Settings</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">
											Customize Pill
										</h3>
										<div className="mb-4">
											<Button
												variant="outline"
												className="w-full justify-start pr-1"
												onClick={toggleDarkMode}
											>
												{isDarkMode ? (
													<Sun className="mr-2 h-4 w-4" />
												) : (
													<Moon className="mr-2 h-4 w-4" />
												)}
												<span>{isDarkMode ? "Light Mode" : "Dark Mode"}</span>
											</Button>
										</div>
										<PillColorSettings
											pillStyle={pillStyle}
											setPillStyle={setPillStyle}
											pillColor={pillColor}
											setPillColor={setPillColor}
											isOutlined={isOutlined}
											setIsOutlined={setIsOutlined}
											isDarkMode={isDarkMode}
											toggleDarkMode={toggleDarkMode}
										/>
									</>
								);
							case "notifications":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("settings")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back to Settings</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">
											Notifications
										</h3>
										<div className="flex items-center justify-between">
											<Label htmlFor="show-notifications">
												Show Notifications
											</Label>
											<Switch
												id="show-notifications"
												checked={showNotifications}
												onCheckedChange={setShowNotifications}
											/>
										</div>
									</>
								);
							case "autoSave":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("settings")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back to Settings</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">Auto Save</h3>
										<div className="flex items-center justify-between">
											<Label htmlFor="auto-save">Auto Save</Label>
											<Switch
												id="auto-save"
												checked={autoSave}
												onCheckedChange={setAutoSave}
											/>
										</div>
									</>
								);
							case "fontSize":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("settings")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back to Settings</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">Font Size</h3>
										<div>
											<Label htmlFor="font-size">Font Size</Label>
											<Select value={fontSize} onValueChange={setFontSize}>
												<SelectTrigger id="font-size" className="w-full mt-1">
													<SelectValue placeholder="Select font size" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="small">Small</SelectItem>
													<SelectItem value="medium">Medium</SelectItem>
													<SelectItem value="large">Large</SelectItem>
												</SelectContent>
											</Select>
										</div>
									</>
								);
							case "widgetMarketplace":
								return (
									<>
										<Button
											variant="ghost"
											className="w-full justify-start pr-1 mb-3"
											onClick={() => setCurrentView("main")}
										>
											<ChevronRight className="mr-2 h-4 w-4 rotate-180" />
											<span>Back</span>
										</Button>
										<h3 className="text-lg font-semibold mb-3">
											Widget Marketplace
										</h3>
										<div className="max-h-[60vh] overflow-y-auto pr-2 -mr-2">
											<WidgetMarketplace
												onAddWidget={(widget) => {
													// Handle widget addition here
													console.log("Widget added:", widget);
												}}
											/>
										</div>
									</>
								);
						}
					})()}
				</motion.div>
			</AnimatePresence>
		);
	};

	return (
		<>
			{isDropdownOpen && (
				<div
					className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40"
					onClick={() => setIsDropdownOpen(false)}
				/>
			)}
			<motion.div
				className="fixed left-4 top-4 z-50"
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<AppLogo size={48} alt="BudsCollab Logo" />
			</motion.div>
			<motion.nav
				className="fixed left-4 top-1/3 z-40 flex items-start space-x-4"
				initial={{ opacity: 0, x: -50 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ duration: 0.5 }}
			>
				<div
					className={`rounded-full p-2 shadow-lg w-20 ${getPillStyle(
						pillStyle,
						pillColor,
						isOutlined
					)}`}
				>
					<div className="flex flex-col items-center space-y-2">
						{groups.map((group) => (
							<Button
								key={group.id}
								variant="ghost"
								size="icon"
								className={`rounded-full p-0 h-auto w-auto hover:bg-gray-100 dark:hover:bg-gray-700 ${
									activeGroup === group.id ? "ring-2 ring-primary" : ""
								}`}
								onClick={() => setActiveGroup(group.id)}
							>
								<GroupAvatar seeds={group.seeds} name={group.name} />
							</Button>
						))}

						<DropdownMenu
							open={isDropdownOpen}
							onOpenChange={setIsDropdownOpen}
						>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className={`rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 ${
										isDarkMode ? "text-white" : "text-black"
									} w-14 h-14 p-0 flex items-center justify-center`}
								>
									<Plus className="h-6 w-6" />
								</Button>
							</DropdownMenuTrigger>
							<AnimatePresence>
								{isDropdownOpen && (
									<DropdownMenuContent
										asChild
										side="right"
										align="start"
										sideOffset={16}
										alignOffset={-230}
										forceMount
									>
										<div className="w-72 p-3 rounded-lg shadow-xl bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
											{renderDropdownContent()}
										</div>
									</DropdownMenuContent>
								)}
							</AnimatePresence>
						</DropdownMenu>
					</div>
				</div>
			</motion.nav>
		</>
	);
}
