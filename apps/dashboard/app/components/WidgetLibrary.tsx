"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
	FileText,
	CheckSquare,
	Clock,
	Calendar,
	BarChart3,
	Thermometer,
	Globe,
	Edit3,
	Plus,
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "./ui/card";
import { Badge } from "./ui/badge";
import { WidgetType } from "../types/grid";
import {
	getSupportedSizes,
	getDefaultSize,
	getSizeLabel,
} from "../config/widgetSizes";

interface WidgetDefinition {
	type: WidgetType;
	name: string;
	description: string;
	icon: React.ComponentType<{ className?: string }>;
	category: string;
}

interface WidgetLibraryProps {
	onAddWidget: (
		type: string,
		title: string,
		defaultSize: { w: number; h: number }
	) => void;
}

interface WidgetDefinitionWithColors extends WidgetDefinition {
	colors: {
		primary: string;
		secondary: string;
		accent: string;
		background: string;
	};
}

const widgetDefinitions: WidgetDefinitionWithColors[] = [
	{
		type: "collaborative-editor",
		name: "Collaborative Editor",
		description: "Real-time collaborative text editor with shared tasks",
		icon: Edit3,
		category: "Productivity",
		colors: {
			primary: "bg-slate-900",
			secondary: "bg-slate-50",
			accent: "border-slate-200 hover:border-slate-900",
			background: "bg-white",
		},
	},
	{
		type: "task-list",
		name: "Task List",
		description: "Simple task management with checkboxes",
		icon: CheckSquare,
		category: "Productivity",
		colors: {
			primary: "bg-emerald-600",
			secondary: "bg-emerald-50",
			accent: "border-emerald-200 hover:border-emerald-600",
			background: "bg-white",
		},
	},
	{
		type: "notes",
		name: "Quick Notes",
		description: "Simple note-taking widget",
		icon: FileText,
		category: "Productivity",
		colors: {
			primary: "bg-amber-500",
			secondary: "bg-amber-50",
			accent: "border-amber-200 hover:border-amber-500",
			background: "bg-white",
		},
	},
	{
		type: "clock",
		name: "Clock",
		description: "Digital clock with timezone support",
		icon: Clock,
		category: "Utilities",
		colors: {
			primary: "bg-blue-600",
			secondary: "bg-blue-50",
			accent: "border-blue-200 hover:border-blue-600",
			background: "bg-white",
		},
	},
	{
		type: "calendar",
		name: "Calendar",
		description: "Monthly calendar view",
		icon: Calendar,
		category: "Utilities",
		colors: {
			primary: "bg-rose-600",
			secondary: "bg-rose-50",
			accent: "border-rose-200 hover:border-rose-600",
			background: "bg-white",
		},
	},
	{
		type: "chart",
		name: "Chart",
		description: "Data visualization charts",
		icon: BarChart3,
		category: "Analytics",
		colors: {
			primary: "bg-violet-600",
			secondary: "bg-violet-50",
			accent: "border-violet-200 hover:border-violet-600",
			background: "bg-white",
		},
	},
	{
		type: "weather",
		name: "Weather",
		description: "Current weather conditions",
		icon: Thermometer,
		category: "Utilities",
		colors: {
			primary: "bg-sky-600",
			secondary: "bg-sky-50",
			accent: "border-sky-200 hover:border-sky-600",
			background: "bg-white",
		},
	},
	{
		type: "web-view",
		name: "Web View",
		description: "Embed web content",
		icon: Globe,
		category: "Utilities",
		colors: {
			primary: "bg-green-600",
			secondary: "bg-green-50",
			accent: "border-green-200 hover:border-green-600",
			background: "bg-white",
		},
	},
];

const categories = Array.from(
	new Set(widgetDefinitions.map((w) => w.category))
);

const categoryColors = {
	All: "bg-slate-900 text-white",
	Productivity: "bg-emerald-600 text-white",
	Utilities: "bg-blue-600 text-white",
	Analytics: "bg-violet-600 text-white",
};

export default function WidgetLibrary({ onAddWidget }: WidgetLibraryProps) {
	const [selectedCategory, setSelectedCategory] = useState<string>("All");

	const allCategories = ["All", ...categories];
	const filteredWidgets =
		selectedCategory === "All"
			? widgetDefinitions
			: widgetDefinitions.filter((w) => w.category === selectedCategory);

	return (
		<div className="max-h-[75vh] overflow-hidden flex flex-col">
			{/* Header */}
			<div className="mb-8">
				<div className="mb-6">
					<p className="text-muted-foreground text-sm">
						Choose a widget to add to your dashboard
					</p>
				</div>

				{/* Category Filter */}
				<div className="flex gap-1 p-1 bg-gray-100 rounded-lg w-fit">
					{allCategories.map((category) => {
						const isSelected = selectedCategory === category;
						const colorClass =
							categoryColors[category as keyof typeof categoryColors] ||
							categoryColors["All"];

						return (
							<button
								key={category}
								onClick={() => setSelectedCategory(category)}
								className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
									isSelected
										? colorClass
										: "text-gray-600 hover:text-gray-900 hover:bg-white"
								}`}
							>
								{category}
							</button>
						);
					})}
				</div>
			</div>

			{/* Widget Grid */}
			<div className="flex-1 overflow-y-auto">
				<div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-6 p-4">
					{filteredWidgets.map((widget) => {
						const defaultSize = getDefaultSize(widget.type);

						return (
							<motion.div
								key={widget.type}
								whileHover={{ scale: 1.05 }}
								whileTap={{ scale: 0.95 }}
								className="flex flex-col items-center cursor-pointer group"
								onClick={() =>
									onAddWidget(widget.type, widget.name, {
										w: defaultSize.w,
										h: defaultSize.h,
									})
								}
							>
								{/* 1x1 Widget Representation */}
								<div
									className={`w-16 h-16 ${widget.colors.secondary} rounded-2xl flex items-center justify-center mb-2 shadow-sm group-hover:shadow-md transition-all duration-200`}
								>
									<widget.icon className="h-8 w-8 text-gray-700" />
								</div>

								{/* Widget Name */}
								<span className="text-xs font-medium text-gray-900 text-center leading-tight max-w-full">
									{widget.name}
								</span>
							</motion.div>
						);
					})}
				</div>
			</div>
		</div>
	);
}
